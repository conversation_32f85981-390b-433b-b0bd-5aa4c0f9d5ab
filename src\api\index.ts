import axios, { AxiosResponse } from 'axios'
import type { ServiceResponse, FileType } from '@/types'

// 创建axios实例
const createApiInstance = (baseURL: string) => {
  return axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json'
    }
  })
}

// 通用API类
export class ApiService {
  private api: any

  constructor(baseURL: string) {
    this.api = createApiInstance(baseURL)
  }

  // 上传文件
  async uploadFile(file: File, fileType: FileType): Promise<ServiceResponse> {
    const formData = new FormData()
    formData.append('file', file)
    
    const response: AxiosResponse<ServiceResponse> = await this.api.post(
      `/FileService/uploadfile?fileType=${fileType}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    )
    return response.data
  }

  // 获取文件列表
  async getFileList(): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.get('/FileService/listfile')
    return response.data
  }

  // 下载结果文件
  async downloadResultFile(): Promise<Blob> {
    const response = await this.api.get('/FileService/downloadfile', {
      responseType: 'blob'
    })
    return response.data
  }

  // 启动处理
  async executeProcess(): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.get('/ExcelProcess/execute')
    return response.data
  }

  // 查询处理状态
  async getProcessStatus(): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.get('/ExcelProcess/status')
    return response.data
  }

  // 下载配置文件
  async downloadConfigFile(fileName: string): Promise<Blob> {
    const response = await this.api.get(`/MaintainService/downloadfile?fileName=${fileName}`, {
      responseType: 'blob'
    })
    return response.data
  }

  // NG模块特有方法
  async getDictValue(iniSpace: string, key: string): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.get(
      `/DicService/dicget?iniSpace=${iniSpace}&key=${key}`
    )
    return response.data
  }

  async getDictInfo(): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.get('/DicService/dicinfo')
    return response.data
  }

  async addDictKey(iniSpace: string, key: string, value: string): Promise<ServiceResponse> {
    const response: AxiosResponse<ServiceResponse> = await this.api.post(
      `/DicService/adddickey?iniSpace=${iniSpace}&key=${key}&value=${value}`
    )
    return response.data
  }
} 