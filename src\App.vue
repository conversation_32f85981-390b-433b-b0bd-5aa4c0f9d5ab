<script setup lang="ts">
import { ref } from 'vue'
import ModulePanel from '@/components/ModulePanel.vue'
import { moduleConfigs } from '@/config/modules'

const activeTab = ref('NG')
</script>

<template>
  <div id="app">
    <div class="app-header">
      <div class="header-content">
        <h1 class="app-title">ECES 增效处理系统</h1>
        <p class="app-subtitle">Excel增效处理服务管理平台</p>
      </div>
    </div>

    <div class="app-container">
      <el-tabs v-model="activeTab" type="card" class="module-tabs">
        <el-tab-pane 
          v-for="module in moduleConfigs" 
          :key="module.name"
          :label="module.title" 
          :name="module.name"
        >
          <ModulePanel :module-config="module" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

#app {
  min-height: 100vh;
}

.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 40px 0;
  text-align: center;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

.app-title {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-subtitle {
  font-size: 18px;
  opacity: 0.9;
  font-weight: 300;
}

.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 24px;
}

.module-tabs {
  background: transparent;
}

.module-tabs .el-tabs__header {
  margin-bottom: 32px;
}

.module-tabs .el-tabs__nav-wrap {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.module-tabs .el-tabs__item {
  font-size: 16px;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.module-tabs .el-tabs__item.is-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.module-tabs .el-tabs__content {
  padding: 0;
}

/* Element Plus 组件样式覆盖 */
.el-button {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.el-progress {
  margin-bottom: 8px;
}

.el-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
}

.el-dialog__headerbtn .el-dialog__close {
  color: white;
}

.el-dialog__body {
  padding: 24px;
}

.el-form-item__label {
  font-weight: 500;
}

.el-input__inner {
  border-radius: 6px;
}

.el-switch {
  --el-switch-on-color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-title {
    font-size: 32px;
  }
  
  .app-subtitle {
    font-size: 16px;
  }
  
  .app-container {
    padding: 20px 16px;
  }
  
  .module-tabs .el-tabs__item {
    font-size: 14px;
    padding: 8px 16px;
  }
}
</style>
