// 环境配置文件
// 通过 .env.development 和 .env.production 文件自动控制环境

export const environmentConfig = {
  // 根据环境变量自动判断是否为生产环境
  isProduction: import.meta.env.VITE_ENV === 'production',

  // 从环境变量获取基础URL
  baseUrl: import.meta.env.VITE_BASE_URL || 'http://127.0.0.1:'
}

// 获取当前环境的完整URL
export const getCurrentBaseUrl = () => {
  return environmentConfig.baseUrl
}

// 获取环境名称
export const getEnvironmentName = () => {
  return environmentConfig.isProduction ? '生产环境' : '测试环境'
} 
