import type { ModuleConfig } from '@/types'
import { environmentConfig } from './environment'

// NG模块的swagger文档
const ngSwagger = {
  "openapi": "3.0.1",
  "info": {
    "title": "ECES",
    "version": "v1"
  },
  "paths": {
    "/DicService/dicget": {
      "get": {
        "tags": ["DicService"],
        "summary": "获取字典值",
        "parameters": [
          {
            "name": "iniSpace",
            "in": "query",
            "description": "字典命名空间（英文sheet名称）",
            "required": true,
            "schema": {
              "type": "string",
              "default": "public"
            }
          },
          {
            "name": "key",
            "in": "query",
            "description": "字典key",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/DicService/dicinfo": {
      "get": {
        "tags": ["DicService"],
        "summary": "获取dic全部内容",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/DicService/adddickey": {
      "post": {
        "tags": ["DicService"],
        "summary": "添加字典值",
        "parameters": [
          {
            "name": "iniSpace",
            "in": "query",
            "description": "字典类别（英文sheet名称）",
            "required": true,
            "schema": {
              "type": "string",
              "default": "public"
            }
          },
          {
            "name": "key",
            "in": "query",
            "description": "字典key",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          },
          {
            "name": "value",
            "in": "query",
            "description": "字典value",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/ExcelProcess/execute": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "excel处理启动接口（数据、模板文件必须完整）",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/ExcelProcess/status": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "excel处理状态查询",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/listfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "数据、模板、结果文件列表",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/uploadfile": {
      "post": {
        "tags": ["FileService"],
        "summary": "上传文件",
        "parameters": [
          {
            "name": "fileType",
            "in": "query",
            "description": "data(数据文件)/template(模板文件)",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": ["file"],
                "type": "object",
                "properties": {
                  "file": {
                    "type": "string",
                    "format": "binary"
                  }
                }
              },
              "encoding": {
                "file": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/downloadfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "下载结果文件",
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/MaintainService/downloadfile": {
      "get": {
        "tags": ["MaintainService"],
        "summary": "下载配置文件",
        "parameters": [
          {
            "name": "fileName",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "ServiceResponse": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "响应信息",
            "nullable": true
          },
          "data": {
            "description": "响应数据",
            "nullable": true
          },
          "code": {
            "type": "integer",
            "description": "响应代码",
            "format": "int32"
          }
        },
        "additionalProperties": false,
        "description": "服务响应实体"
      }
    }
  },
  "tags": [
    {
      "name": "DicService",
      "description": "字典相关操作服务接口"
    },
    {
      "name": "ExcelProcess",
      "description": "excel操作服务接口"
    },
    {
      "name": "FileService",
      "description": "项目文件相关服务接口"
    },
    {
      "name": "MaintainService",
      "description": "运维服务"
    }
  ]
}

// OIL模块的swagger文档
const oilSwagger = {
  "openapi": "3.0.1",
  "info": {
    "title": "ECES-Oil",
    "description": "<h2>Excel增效处理服务 Oil版<p>操作步骤<p>1.上传配置文件和数据文件；2.启动处理接口；3.查询处理状态；4.下载结果文件</p></p></h2>",
    "version": "v1"
  },
  "paths": {
    "/ExcelProcess/execute": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "第二步：Excel处理启动接口（启动前保证数据、模板文件必须完整上传）",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/ExcelProcess/status": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "第三步：Excel处理状态查询",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/listfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "数据、模板、结果文件列表",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/uploadfile": {
      "post": {
        "tags": ["FileService"],
        "summary": "第一步:上传文件 (数据文件和模板文件)",
        "parameters": [
          {
            "name": "fileType",
            "in": "query",
            "description": "data(数据文件)/template(模板文件)",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": ["file"],
                "type": "object",
                "properties": {
                  "file": {
                    "type": "string",
                    "format": "binary"
                  }
                }
              },
              "encoding": {
                "file": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/downloadfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "第四步：下载结果文件",
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/MaintainService/downloadfile": {
      "get": {
        "tags": ["MaintainService"],
        "summary": "下载配置文件",
        "parameters": [
          {
            "name": "fileName",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "ServiceResponse": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "响应信息",
            "nullable": true
          },
          "data": {
            "description": "响应数据",
            "nullable": true
          },
          "code": {
            "type": "integer",
            "description": "响应代码",
            "format": "int32"
          }
        },
        "additionalProperties": false,
        "description": "服务响应实体"
      }
    }
  },
  "tags": [
    {
      "name": "ExcelProcess",
      "description": "excel操作服务接口"
    },
    {
      "name": "FileService",
      "description": "项目文件相关服务接口"
    },
    {
      "name": "MaintainService",
      "description": "运维服务"
    }
  ]
}

// LPG模块的swagger文档
const lpgSwagger = {
  "openapi": "3.0.1",
  "info": {
    "title": "ECES-LPG",
    "description": "<h2>Excel增效处理服务 LPG版<p>操作步骤<p>1.上传配置文件和数据文件；2.启动处理接口；3.查询处理状态；4.下载结果文件</p></p></h2>",
    "version": "v1"
  },
  "paths": {
    "/ExcelProcess/execute": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "⑵第二步：Excel处理启动接口（启动前保证数据、模板文件必须完整上传）",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/ExcelProcess/status": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "⑶第三步：Excel处理状态查询",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/listfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "数据、模板、结果文件列表",
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/uploadfile": {
      "post": {
        "tags": ["FileService"],
        "summary": "⑴第一步:上传文件 (数据文件和模板文件)",
        "parameters": [
          {
            "name": "fileType",
            "in": "query",
            "description": "data(数据文件)/template(模板文件)",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": ["file"],
                "type": "object",
                "properties": {
                  "file": {
                    "type": "string",
                    "format": "binary"
                  }
                }
              },
              "encoding": {
                "file": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "OK",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/downloadfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "⑷第四步：下载结果文件",
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    },
    "/MaintainService/downloadfile": {
      "get": {
        "tags": ["MaintainService"],
        "summary": "下载配置文件",
        "parameters": [
          {
            "name": "fileName",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "ServiceResponse": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "响应信息",
            "nullable": true
          },
          "data": {
            "description": "响应数据",
            "nullable": true
          },
          "code": {
            "type": "integer",
            "description": "响应代码",
            "format": "int32"
          }
        },
        "additionalProperties": false,
        "description": "服务响应实体"
      }
    }
  },
  "tags": [
    {
      "name": "ExcelProcess",
      "description": "excel操作服务接口"
    },
    {
      "name": "FileService",
      "description": "项目文件相关服务接口"
    },
    {
      "name": "MaintainService",
      "description": "运维服务"
    }
  ]
}

// MTBE模块的swagger文档
const mtbeSwagger = {
  "openapi": "3.0.1",
  "info": {
    "title": "ECES-MTBE",
    "description": "<h2>Excel增效处理服务 MTBE版<p>操作步骤<p>1.上传配置文件和数据文件；2.启动处理接口；3.查询处理状态；4.下载结果文件</p></p></h2>",
    "version": "v1"
  },
  "paths": {
    "/ExcelProcess/execute": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "⑵第二步：Excel处理启动接口（启动前保证数据、模板文件必须完整上传）",
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/ExcelProcess/status": {
      "get": {
        "tags": ["ExcelProcess"],
        "summary": "⑶第三步：Excel处理状态查询",
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/listfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "数据、模板、结果文件列表",
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/uploadfile": {
      "post": {
        "tags": ["FileService"],
        "summary": "⑴第一步:上传文件 (数据文件和模板文件)",
        "parameters": [
          {
            "name": "fileType",
            "in": "query",
            "description": "data(数据文件)/template(模板文件)",
            "required": true,
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "multipart/form-data": {
              "schema": {
                "required": ["file"],
                "type": "object",
                "properties": {
                  "file": {
                    "type": "string",
                    "format": "binary"
                  }
                }
              },
              "encoding": {
                "file": {
                  "style": "form"
                }
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ServiceResponse"
                }
              }
            }
          }
        }
      }
    },
    "/FileService/downloadfile": {
      "get": {
        "tags": ["FileService"],
        "summary": "⑷第四步：下载结果文件",
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    },
    "/MaintainService/downloadfile": {
      "get": {
        "tags": ["MaintainService"],
        "summary": "下载配置文件",
        "parameters": [
          {
            "name": "fileName",
            "in": "query",
            "required": true,
            "schema": {
              "type": "string",
              "default": ""
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success"
          }
        }
      }
    }
  },
  "components": {
    "schemas": {
      "ServiceResponse": {
        "type": "object",
        "properties": {
          "message": {
            "type": "string",
            "description": "响应信息",
            "nullable": true
          },
          "data": {
            "description": "响应数据",
            "nullable": true
          },
          "code": {
            "type": "integer",
            "description": "响应代码",
            "format": "int32"
          }
        },
        "additionalProperties": false,
        "description": "服务响应实体"
      }
    }
  },
  "tags": [
    {
      "name": "ExcelProcess",
      "description": "excel操作服务接口"
    },
    {
      "name": "FileService",
      "description": "项目文件相关服务接口"
    },
    {
      "name": "MaintainService",
      "description": "运维服务"
    }
  ]
}

// 获取模块URL
const getModuleUrl = (port: string) => {
  return environmentConfig.baseUrl + port + '/'
}

// 模块配置
export const moduleConfigs: ModuleConfig[] = [
  {
    name: 'NG',
    title: 'NG模块',
    baseUrl: getModuleUrl('9663'),
    swagger: ngSwagger
  },
  {
    name: 'OIL',
    title: 'OIL模块',
    baseUrl: getModuleUrl('9664'),
    swagger: oilSwagger
  },
  {
    name: 'LPG',
    title: 'LPG模块',
    baseUrl: getModuleUrl('9665'),
    swagger: lpgSwagger
  },
  {
    name: 'MTBE',
    title: 'MTBE模块',
    baseUrl: getModuleUrl('9666'),
    swagger: mtbeSwagger
  }
] 