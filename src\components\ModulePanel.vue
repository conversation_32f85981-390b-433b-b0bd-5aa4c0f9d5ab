<template>
  <div class="module-panel">
    <div class="module-header">
      <h2>{{ moduleConfig.title }}</h2>
      <div class="environment-info">
        <span class="env-badge">{{ getEnvironmentName() }}</span>
      </div>
    </div>

    <div class="module-content">
      <!-- 文件上传区域 -->
      <div class="upload-section">
        <h3>文件上传</h3>
        <div class="upload-buttons">
          <el-upload
            ref="dataUploadRef"
            :action="uploadAction"
            :data="{ fileType: 'data' }"
            :show-file-list="false"
            :on-success="handleDataUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls,.csv">
            <el-button type="primary" :loading="uploadingData">
              <el-icon><Upload /></el-icon>
              上传数据文件
            </el-button>
          </el-upload>

          <el-upload
            ref="templateUploadRef"
            :action="uploadAction"
            :data="{ fileType: 'template' }"
            :show-file-list="false"
            :on-success="handleTemplateUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls,.csv">
            <el-button type="primary" :loading="uploadingTemplate">
              <el-icon><Upload /></el-icon>
              上传模板文件
            </el-button>
          </el-upload>
        </div>
      </div>

      <!-- 文件列表 -->
      <div class="file-list-section">
        <h3>文件列表</h3>
        <el-button @click="refreshFileList" :loading="loadingFiles" type="primary">
          <el-icon><Refresh /></el-icon>
          获取文件列表
        </el-button>
        <div v-if="fileList && !loadingFiles" class="file-summary">
          <p>数据文件: {{ fileList.数据文件?.length || 0 }} 个</p>
          <p>模板文件: {{ fileList.模板文件?.length || 0 }} 个</p>
          <p>结果文件: {{ fileList.结果文件?.length || 0 }} 个</p>
        </div>
        <div v-else-if="!loadingFiles" class="no-files">
          <p>暂无文件，请点击上方按钮获取文件列表</p>
        </div>
      </div>

      <!-- 处理控制区域 -->
      <div class="process-section">
        <h3>处理控制</h3>
        <div class="process-buttons">
          <el-button type="success" @click="startProcess" :loading="processing" :disabled="!canStartProcess">
            <el-icon><VideoPlay /></el-icon>
            启动处理
          </el-button>

          <el-button type="primary" @click="queryLastProcessStatus" :loading="queryingStatus">
            <el-icon><Search /></el-icon>
            上次处理状态查询
          </el-button>

          <el-button type="warning" @click="downloadResult" :disabled="processing">
            <el-icon><Download /></el-icon>
            下载结果文件
          </el-button>

          <el-button type="info" @click="downloadConfig">
            <el-icon><Setting /></el-icon>
            下载配置文件
          </el-button>
        </div>

        <!-- 处理状态显示 -->
        <div v-if="processing" class="process-status">
          <div class="loading-container">
            <div class="spinner">
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
              <div class="spinner-ring"></div>
            </div>
            <p class="loading-text">{{ processMessage }}</p>
          </div>
        </div>
      </div>

      <!-- NG模块特有的字典操作 -->
      <div v-if="moduleConfig.name === 'NG'" class="dict-section">
        <h3>字典操作</h3>
        <div class="dict-controls">
          <el-button @click="getDictValue" type="primary">
            <el-icon><Search /></el-icon>
            获取字典值
          </el-button>
          <el-button @click="getDictInfo" type="primary">
            <el-icon><Document /></el-icon>
            显示字典全部内容
          </el-button>
          <el-button @click="showAddDictDialog" type="success">
            <el-icon><Plus /></el-icon>
            添加字典值
          </el-button>
        </div>
      </div>
    </div>

    <!-- 添加字典对话框 -->
    <el-dialog v-model="addDictDialogVisible" title="添加字典值" width="500px">
      <el-form :model="dictForm" label-width="100px">
        <el-form-item label="命名空间">
          <el-input v-model="dictForm.iniSpace" placeholder="请输入字典命名空间" />
        </el-form-item>
        <el-form-item label="字典Key">
          <el-input v-model="dictForm.key" placeholder="请输入字典Key" />
        </el-form-item>
        <el-form-item label="字典Value">
          <el-input v-model="dictForm.value" placeholder="请输入字典Value" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDictDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addDictKey" :loading="addingDict"> 确定 </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字典内容显示对话框 -->
    <el-dialog v-model="dictInfoDialogVisible" title="字典全部内容" width="800px">
      <div class="dict-content">
        <pre>{{ dictInfoContent }}</pre>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dictInfoDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 文件列表显示对话框 -->
    <el-dialog v-model="fileListDialogVisible" title="文件列表详情" width="900px">
      <div class="file-list-content">
        <div v-if="fileList" class="file-categories">
          <!-- 数据文件 -->
          <div v-if="fileList.数据文件 && fileList.数据文件.length > 0" class="file-category">
            <h4>数据文件 ({{ fileList.数据文件.length }})</h4>
            <el-table :data="fileList.数据文件" style="width: 100%; margin-bottom: 20px">
              <el-table-column prop="Name" label="文件名" />
              <el-table-column prop="CreateTime" label="创建时间" />
              <el-table-column prop="LastWriteTime" label="修改时间" />
            </el-table>
          </div>

          <!-- 模板文件 -->
          <div v-if="fileList.模板文件 && fileList.模板文件.length > 0" class="file-category">
            <h4>模板文件 ({{ fileList.模板文件.length }})</h4>
            <el-table :data="fileList.模板文件" style="width: 100%; margin-bottom: 20px">
              <el-table-column prop="Name" label="文件名" />
              <el-table-column prop="CreateTime" label="创建时间" />
              <el-table-column prop="LastWriteTime" label="修改时间" />
            </el-table>
          </div>

          <!-- 结果文件 -->
          <div v-if="fileList.结果文件 && fileList.结果文件.length > 0" class="file-category">
            <h4>结果文件 ({{ fileList.结果文件.length }})</h4>
            <el-table :data="fileList.结果文件" style="width: 100%; margin-bottom: 20px">
              <el-table-column prop="Name" label="文件名" />
              <el-table-column prop="CreateTime" label="创建时间" />
              <el-table-column prop="LastWriteTime" label="修改时间" />
            </el-table>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="fileListDialogVisible = false">关闭</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Upload, Download, VideoPlay, Setting, Search, Document, Plus, Refresh } from "@element-plus/icons-vue";
import type { ModuleConfig, FileListResponse } from "@/types";
import { ApiService } from "@/api";
import { getEnvironmentName } from "@/config/environment";

const props = defineProps<{
  moduleConfig: ModuleConfig;
}>();

// 响应式数据
const apiService = ref<ApiService>();
const fileList = ref<FileListResponse | null>(null);
const loadingFiles = ref(false);
const fileListDialogVisible = ref(false);
const uploadingData = ref(false);
const uploadingTemplate = ref(false);
const processing = ref(false);
const processMessage = ref("");
const addDictDialogVisible = ref(false);
const addingDict = ref(false);
const dictInfoDialogVisible = ref(false);
const dictInfoContent = ref("");
const queryingStatus = ref(false);

const dictForm = reactive({
  iniSpace: "",
  key: "",
  value: "",
});

// 计算属性
const uploadAction = computed(() => {
  return `${props.moduleConfig.baseUrl}FileService/uploadfile`;
});

const canStartProcess = computed(() => {
  return (
    fileList.value &&
    fileList.value.数据文件 &&
    fileList.value.数据文件.length > 0 &&
    fileList.value.模板文件 &&
    fileList.value.模板文件.length > 0
  );
});

const beforeUpload = (file: File) => {
  const isValidType = /\.(xlsx|xls|csv)$/.test(file.name.toLowerCase());
  if (!isValidType) {
    ElMessage.error("只能上传Excel或CSV文件!");
    return false;
  }
  return true;
};

const handleDataUploadSuccess = (response: any, _file: File) => {
  if (response.code === 200) {
    ElMessage.success("数据文件上传成功");
    refreshFileList();
  } else {
    ElMessage.error(response.message || "上传失败");
  }
};

const handleTemplateUploadSuccess = (response: any, _file: File) => {
  if (response.code === 200) {
    ElMessage.success("模板文件上传成功");
    refreshFileList();
  } else {
    ElMessage.error(response.message || "上传失败");
  }
};

const handleUploadError = () => {
  ElMessage.error("上传失败，请检查网络连接");
};

const refreshFileList = async (showDialog = true) => {
  if (!apiService.value) return;

  loadingFiles.value = true;
  try {
    const response = await apiService.value.getFileList();
    if (response.code === 200) {
      fileList.value = response.data;
      if (showDialog) {
        fileListDialogVisible.value = true;
        ElMessage.success("获取文件列表成功");
      }
    } else {
      ElMessage.error(response.message || "获取文件列表失败");
    }
  } catch (error) {
    ElMessage.error("获取文件列表失败");
  } finally {
    loadingFiles.value = false;
  }
};

const startProcess = async () => {
  if (!apiService.value) return;

  processing.value = true;
  processMessage.value = "正在启动处理...";

  try {
    const response = await apiService.value.executeProcess();
    if (response.code === 200) {
      processMessage.value = "处理已启动，正在轮询状态...";
      pollProcessStatus();
    } else {
      processMessage.value = response.message || "启动处理失败";
      processing.value = false;
    }
  } catch (error) {
    processMessage.value = "启动处理失败";
    processing.value = false;
  }
};

const pollProcessStatus = async () => {
  if (!apiService.value) return;

  const poll = async () => {
    try {
      const response = await apiService.value!.getProcessStatus();
      if (response.code === 200) {
        // 成功情况：data包含处理状态信息
        if (response.data && response.data.status === "处理完成") {
          processMessage.value = "处理完成";
          processing.value = false;

          // 显示成功弹窗
          ElMessageBox.alert(
            `处理完成！\n\n开始时间: ${response.data.startTime}\n结束时间: ${response.data.endTime}\n处理状态: ${response.data.status}`,
            "处理成功",
            {
              confirmButtonText: "确定",
              type: "success",
              customClass: "success-dialog",
            }
          );

          ElMessage.success("处理完成");
          refreshFileList(false);
        } else if (response.data && response.data.status === "处理失败") {
          // 处理失败状态
          processMessage.value = "处理失败";
          processing.value = false;

          // 显示失败弹窗
          ElMessageBox.alert(
            `处理失败！\n\n开始时间: ${response.data.startTime}\n结束时间: ${response.data.endTime}\n处理状态: ${
              response.data.status
            }\n错误信息: ${response.data.message || "未知错误"}`,
            "处理失败",
            {
              confirmButtonText: "确定",
              type: "error",
              customClass: "error-dialog",
            }
          );

          ElMessage.error("处理失败");
        } else if (response.data && response.data.status === "正在处理中") {
          // 处理中状态
          processMessage.value = `正在处理中... ${response.data.message || ""}`;
          setTimeout(poll, 2000); // 2秒后继续轮询
        } else {
          // 其他状态
          processMessage.value = response.data?.message || "正在处理中...";
          setTimeout(poll, 2000);
        }
      } else {
        // 失败情况：data是错误信息
        processMessage.value = "查询状态失败";
        processing.value = false;

        // 显示失败弹窗
        ElMessageBox.alert(`查询状态失败！\n\n错误信息: ${response.data || response.message || "未知错误"}`, "处理失败", {
          confirmButtonText: "确定",
          type: "error",
          customClass: "error-dialog",
        });

        ElMessage.error("查询状态失败");
      }
    } catch (error) {
      processMessage.value = "查询状态失败";
      processing.value = false;

      // 显示失败弹窗
      ElMessageBox.alert(`查询状态失败！\n\n错误信息: ${error instanceof Error ? error.message : "网络错误"}`, "处理失败", {
        confirmButtonText: "确定",
        type: "error",
        customClass: "error-dialog",
      });

      ElMessage.error("查询状态失败");
    }
  };

  poll();
};

const queryLastProcessStatus = async () => {
  if (!apiService.value) return;

  queryingStatus.value = true;
  processMessage.value = "正在查询上次处理状态...";

  try {
    const response = await apiService.value.getProcessStatus();
    if (response.code === 200) {
      if (response.data && response.data.status === "处理完成") {
        ElMessageBox.alert(
          `上次处理完成！\n\n开始时间: ${response.data.startTime}\n结束时间: ${response.data.endTime}\n处理状态: ${response.data.status}`,
          "上次处理状态",
          {
            confirmButtonText: "确定",
            type: "success",
            customClass: "success-dialog",
          }
        );
        ElMessage.success("上次处理状态查询成功");
      } else if (response.data && response.data.status === "正在处理中") {
        ElMessageBox.alert(`正在处理中！\n\n开始时间: ${response.data.startTime}`, "正在处理中", {
          confirmButtonText: "确定",
          type: "info",
          customClass: "info-dialog",
        });
        ElMessage.info("上次处理状态查询成功");
      } else {
        ElMessageBox.alert(
          `上次处理失败！\n\n开始时间: ${response.data?.startTime}\n处理状态: ${response.data?.status}\n错误信息: ${
            response.data?.message || "未知错误"
          }`,
          "上次处理状态",
          {
            confirmButtonText: "确定",
            type: "error",
            customClass: "error-dialog",
          }
        );
        ElMessage.error("上次处理状态查询失败");
      }
    } else {
      ElMessageBox.alert(
        `查询上次处理状态失败！\n\n错误信息: ${response.data || response.message || "未知错误"}`,
        "上次处理状态",
        {
          confirmButtonText: "确定",
          type: "error",
          customClass: "error-dialog",
        }
      );
      ElMessage.error("上次处理状态查询失败");
    }
  } catch (error) {
    ElMessageBox.alert(
      `查询上次处理状态失败！\n\n错误信息: ${error instanceof Error ? error.message : "网络错误"}`,
      "上次处理状态",
      {
        confirmButtonText: "确定",
        type: "error",
        customClass: "error-dialog",
      }
    );
    ElMessage.error("上次处理状态查询失败");
  } finally {
    queryingStatus.value = false;
  }
};

const downloadResult = async () => {
  if (!apiService.value) return;

  try {
    const blob = await apiService.value.downloadResultFile();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = "result.zip";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    ElMessage.success("下载成功");
  } catch (error) {
    ElMessage.error("下载失败");
  }
};

const downloadConfig = async () => {
  try {
    const fileName = await ElMessageBox.prompt("请输入要下载的配置文件名", "下载配置文件", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPattern: /.+/,
      inputErrorMessage: "文件名不能为空",
    });

    if (!apiService.value) return;

    const blob = await apiService.value.downloadConfigFile(fileName.value);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = fileName.value;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    ElMessage.success("下载成功");
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("下载失败");
    }
  }
};

// NG模块特有的字典操作
const getDictValue = async () => {
  try {
    const { value: iniSpace } = await ElMessageBox.prompt("请输入字典命名空间", "获取字典值", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPattern: /.+/,
      inputErrorMessage: "命名空间不能为空",
    });

    const { value: key } = await ElMessageBox.prompt("请输入字典Key", "获取字典值", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      inputPattern: /.+/,
      inputErrorMessage: "Key不能为空",
    });

    if (!apiService.value) return;

    const response = await apiService.value.getDictValue(iniSpace, key);
    if (response.code === 200) {
      ElMessage.success(`字典值: ${response.data}`);
    } else {
      ElMessage.error(response.message || "获取字典值失败");
    }
  } catch (error) {
    if (error !== "cancel") {
      ElMessage.error("获取字典值失败");
    }
  }
};

const getDictInfo = async () => {
  if (!apiService.value) return;

  try {
    const response = await apiService.value.getDictInfo();
    if (response.code === 200) {
      ElMessage.success("获取字典信息成功");
      dictInfoContent.value = response.data;
      dictInfoDialogVisible.value = true;
    } else {
      ElMessage.error(response.message || "获取字典信息失败");
    }
  } catch (error) {
    ElMessage.error("获取字典信息失败");
  }
};

const showAddDictDialog = () => {
  dictForm.iniSpace = "";
  dictForm.key = "";
  dictForm.value = "";
  addDictDialogVisible.value = true;
};

const addDictKey = async () => {
  if (!apiService.value) return;

  if (!dictForm.iniSpace || !dictForm.key || !dictForm.value) {
    ElMessage.error("请填写完整的字典信息");
    return;
  }

  addingDict.value = true;
  try {
    const response = await apiService.value.addDictKey(dictForm.iniSpace, dictForm.key, dictForm.value);
    if (response.code === 200) {
      ElMessage.success("添加字典值成功");
      addDictDialogVisible.value = false;
    } else {
      ElMessage.error(response.message || "添加字典值失败");
    }
  } catch (error) {
    ElMessage.error("添加字典值失败");
  } finally {
    addingDict.value = false;
  }
};

// 生命周期
onMounted(() => {
  apiService.value = new ApiService(props.moduleConfig.baseUrl);
  // 初始化时获取文件列表，不显示弹窗
  refreshFileList(false);
});
</script>

<style scoped>
.module-panel {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 24px;
}

.module-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.module-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
}

.environment-info {
  display: flex;
  align-items: center;
}

.env-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.module-content {
  padding: 24px;
}

.module-content h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.upload-section,
.file-list-section,
.process-section,
.dict-section {
  margin-bottom: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.upload-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.process-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 16px;
}

.dict-controls {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.process-status {
  margin-top: 16px;
  padding: 16px;
  background: #e8f4fd;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.process-status p {
  margin: 8px 0 0 0;
  color: #666;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.no-files {
  text-align: center;
  padding: 40px 20px;
  color: #999;
  font-size: 14px;
}

.no-files p {
  margin: 0;
}

.dict-content {
  max-height: 400px;
  overflow-y: auto;
  background: #f8f9fa;
  border-radius: 4px;
  padding: 16px;
}

.dict-content pre {
  margin: 0;
  font-family: "Courier New", monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #333;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.file-summary {
  margin-top: 16px;
  padding: 16px;
  background: #e8f4fd;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.file-summary p {
  margin: 8px 0;
  color: #666;
  font-size: 14px;
}

.file-list-content {
  max-height: 500px;
  overflow-y: auto;
}

.file-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-category h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.spinner {
  width: 50px;
  height: 50px;
  position: relative;
}

.spinner-ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  opacity: 0.8;
}

.spinner-ring:nth-child(1) {
  animation-delay: -0.4s;
  border-top-color: #667eea;
}

.spinner-ring:nth-child(2) {
  animation-delay: -0.2s;
  border-top-color: #764ba2;
}

.spinner-ring:nth-child(3) {
  animation-delay: 0s;
  border-top-color: #667eea;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  margin-top: 15px;
  color: #666;
  font-size: 14px;
}

/* 弹窗样式美化 */
:deep(.success-dialog) {
  border-radius: 12px;
}

:deep(.success-dialog .el-message-box__header) {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

:deep(.success-dialog .el-message-box__title) {
  color: white;
  font-weight: 600;
}

:deep(.error-dialog) {
  border-radius: 12px;
}

:deep(.error-dialog .el-message-box__header) {
  background: linear-gradient(135deg, #f56c6c 0%, #f78989 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

:deep(.error-dialog .el-message-box__title) {
  color: white;
  font-weight: 600;
}

:deep(.info-dialog) {
  border-radius: 12px;
}

:deep(.info-dialog .el-message-box__header) {
  background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

:deep(.info-dialog .el-message-box__title) {
  color: white;
  font-weight: 600;
}

:deep(.el-message-box__content) {
  padding: 20px;
  line-height: 1.6;
  white-space: pre-line;
}

:deep(.el-message-box__btns) {
  padding: 0 20px 20px;
}
</style>
